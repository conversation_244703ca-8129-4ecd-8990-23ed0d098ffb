import os
import subprocess
import logging
import sys
import asyncio
from fuzzywuzzy import process

try:
    from livekit.agents import function_tool
except ImportError:
    def function_tool(func): 
        return func

try:
    import win32gui
    import win32con
except ImportError:
    win32gui = None
    win32con = None

try:
    import pygetwindow as gw
except ImportError:
    gw = None

# Setup encoding and logger
sys.stdout.reconfigure(encoding='utf-8')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# App command map
APP_MAPPINGS = {
    "notepad": "notepad",
    "calculator": "calc",
    "chrome": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "edge": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe",
    "vlc": "C:\\Program Files\\VideoLAN\\VLC\\vlc.exe",
    "command prompt": "cmd",
    "control panel": "control",
    "settings": "start ms-settings:",
    "paint": "mspaint",
    "vs code": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",
    "postman": "C:\\Users\\<USER>\\AppData\\Local\\Postman\\Postman.exe",
    # Add more apps as needed
    "windows explorer": "explorer.exe",
    "task manager": "taskmgr",
    "powershell": "powershell.exe",
    "word": "winword.exe",
    "excel": "excel.exe",
    "powerpoint": "powerpnt.exe",
    "onedrive": "OneDrive.exe",
    "outlook": "outlook.exe",
    "teams": "teams.exe",
    "zoom": "zoom.exe",
    "skype": "lync.exe",
    "slack": "slack.exe",
    "discord": "Discord.exe",
    "spotify": "Spotify.exe",
    


}

# -------------------------
# Global focus utility
# -------------------------
async def focus_window(title_keyword: str) -> bool:
    if not gw:
        logger.warning("⚠ pygetwindow")
        return False

    await asyncio.sleep(1.5)  # Give time for window to appear
    title_keyword = title_keyword.lower().strip()

    for window in gw.getAllWindows():
        if title_keyword in window.title.lower():
            if window.isMinimized:
                window.restore()
            window.activate()
            return True
    return False

# Index files/folders
async def index_items(base_dirs):
    item_index = []
    for base_dir in base_dirs:
        for root, dirs, files in os.walk(base_dir):
            for d in dirs:
                item_index.append({"name": d, "path": os.path.join(root, d), "type": "folder"})
            for f in files:
                item_index.append({"name": f, "path": os.path.join(root, f), "type": "file"})
    logger.info(f"✅ Indexed {len(item_index)} items.")
    return item_index

async def search_item(query, index, item_type):
    filtered = [item for item in index if item["type"] == item_type]
    choices = [item["name"] for item in filtered]
    if not choices:
        return None
    best_match, score = process.extractOne(query, choices)
    logger.info(f"🔍 Matched '{query}' to '{best_match}' with score {score}")
    if score > 70:
        for item in filtered:
            if item["name"] == best_match:
                return item
    return None

# File/folder actions
async def open_folder(path):
    try:
        os.startfile(path) if os.name == 'nt' else subprocess.call(['xdg-open', path])
        await focus_window(os.path.basename(path))
    except Exception as e:
        logger.error(f"❌ Sir file open karne mein error aaya hai: {e}")

async def play_file(path):
    try:
        os.startfile(path) if os.name == 'nt' else subprocess.call(['xdg-open', path])
        await focus_window(os.path.basename(path))
    except Exception as e:
        logger.error(f"❌ Sir file open karne mein error aaya hai: {e}")

async def create_folder(path):
    try:
        os.makedirs(path, exist_ok=True)
        return f"✅ Sir folder create ho gaya hai: {path}"
    except Exception as e:
        return f"❌ Sir folder create karne mein error aaya hai: {e}"

async def rename_item(old_path, new_path):
    try:
        os.rename(old_path, new_path)
        return f"✅ Sir naam change kar ke {new_path} kar diya hai."
    except Exception as e:
        return f"❌ Sir naam change nahi hua: {e}"

async def delete_item(path):
    try:
        if os.path.isdir(path):
            os.rmdir(path)
        else:
            os.remove(path)
        return f"🗑️ Deleted: {path}"
    except Exception as e:
        return f"❌ Sir delete nahi hua: {e}"

async def open_folder_in_vscode(folder_path):
    """Open a folder in VS Code"""
    try:
        vscode_path = APP_MAPPINGS.get("vs code", "code")

        # Use 'code' command if available, otherwise use full path
        if os.path.exists(vscode_path):
            cmd = f'"{vscode_path}" "{folder_path}"'
        else:
            # Try using 'code' command (if VS Code is in PATH)
            cmd = f'code "{folder_path}"'

        process = await asyncio.create_subprocess_shell(
            cmd,
            shell=True,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.wait()

        # Focus VS Code window
        await asyncio.sleep(2)  # Give VS Code time to open
        await focus_window("Visual Studio Code")

        logger.info(f"📁 VS Code mein folder open ho gaya: {folder_path}")
        return f"✅ VS Code mein folder open ho gaya: {os.path.basename(folder_path)}"

    except Exception as e:
        logger.error(f"❌ VS Code mein folder open karne mein error: {e}")
        return f"❌ VS Code mein folder open nahi ho saka: {e}"

# App control
@function_tool
async def open(app_title: str) -> str:
    app_title = app_title.lower().strip()
    app_command = APP_MAPPINGS.get(app_title, app_title)
    try:
        await asyncio.create_subprocess_shell(f'start "" "{app_command}"', shell=True)
        focused = await focus_window(app_title)
        if focused:
            return f"🚀 Sir app open ho gaya hai: {app_title}."
        else:
            return f"🚀 {app_title} open ho gaya hai."
    except Exception as e:
        return f"❌ {app_title} open nahi ho paya: {e}"

@function_tool
async def close(app_name: str) -> str:
    if not win32gui:
        return "❌ win32gui"

    def enumHandler(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            if app_name.lower() in win32gui.GetWindowText(hwnd).lower():
                win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)

    win32gui.EnumWindows(enumHandler, None)
    return f"❌ Window bandh ho gayi hai: {app_name}"

# VS Code specific function
@function_tool
async def open_in_vscode(folder_name: str) -> str:
    """Open a folder in VS Code by searching for it"""
    folders_to_index = ["D:/", "D:/Projects", "D:/Projects/Fantasy Creators", "D:/Projects/Personal", ]  # Common project locations
    index = await index_items(folders_to_index)

    # Search for the folder
    item = await search_item(folder_name, index, "folder")
    if item:
        return await open_folder_in_vscode(item["path"])
    else:
        # If not found, try to open the path directly if it exists
        if os.path.exists(folder_name):
            return await open_folder_in_vscode(folder_name)
        return f"❌ Folder '{folder_name}' nahi mila."

# Jarvis command logic
@function_tool
async def folder_file(query: str) -> str:
    folders_to_index = ["D:/"]
    index = await index_items(folders_to_index)
    command_lower = query.lower()

    if "create folder" in command_lower:
        folder_name = query.replace("create folder", "").strip()
        path = os.path.join("D:/", folder_name)
        return await create_folder(path)

    if "rename" in command_lower:
        parts = command_lower.replace("rename", "").strip().split("to")
        if len(parts) == 2:
            old_name = parts[0].strip()
            new_name = parts[1].strip()
            item = await search_item(old_name, index, "folder")
            if item:
                new_path = os.path.join(os.path.dirname(item["path"]), new_name)
                return await rename_item(item["path"], new_path)
        return "❌ rename command valid nahi hai."

    if "delete" in command_lower:
        item = await search_item(query, index, "folder") or await search_item(query, index, "file")
        if item:
            return await delete_item(item["path"])
        return "❌ Delete karne ke liye item nahi mila."

    if "open in vscode" in command_lower or "vscode" in command_lower:
        folder_name = query.replace("open in vscode", "").replace("vscode", "").strip()
        item = await search_item(folder_name, index, "folder")
        if item:
            return await open_folder_in_vscode(item["path"])
        return "❌ VS Code ke liye folder nahi mila."

    if "folder" in command_lower or "open folder" in command_lower:
        item = await search_item(query, index, "folder")
        if item:
            await open_folder(item["path"])
            return f"✅ Folder opened: {item['name']}"
        return "❌ Folder nahi mila."

    item = await search_item(query, index, "file")
    if item:
        await play_file(item["path"])
        return f"✅ File opened: {item['name']}"

    return "⚠ Kuch bhi match nahi hua."


