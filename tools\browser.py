# open websites in browser and google youtube queries
import webbrowser
import os
import logging
from livekit.agents import function_tool
import sys


try:
    import pywhatkit as kit
except ImportError:
    kit = None

# Setup encoding and logger
sys.stdout.reconfigure(encoding='utf-8')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@function_tool
async def open_website_browser(url: str) -> str:
    try:
        webbrowser.open(url)
        return f"Website open ho gaya: {url}"
    except Exception as e:
        return f"❌ Website open nahi ho paya: {e}"

@function_tool
async def google_search_browser(query: str) -> str:
    try:
        kit.search(query)
        return f"Google search ho gayi: {query}"
    except Exception as e:
        return f"❌ Google search mein error: {e}"

@function_tool
async def youtube_search_browser(video_name: str) -> str:
    try:
        kit.playonyt(video_name)
        return f"Youtube video play ho gaya: {video_name}"
    except Exception as e:
        return f"❌ Youtube video play nahi ho paya: {e}"

